import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import usePagination from '../../hooks/usePagination';
import {
  getProducts,
  updateProduct,
  deleteProduct,
} from '../../apis/admin';
import { catchAsync, handelFormData, showSuccess } from '../../utils/helper';
import ProductForm from '../../components/admin/ProductForm';
import { Category, Product, ProductFormData, Question, defaultPaginationValues, mapProductToFormData } from '../../types/global';
import { getCategories, getQuestions } from '../../apis/admin';

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
  const [searchText, setSearchText] = useState('');
  const [visibleDialog, setVisibleDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  // formData holds the initial data for the ProductForm.
  const [formData, setFormData] = useState<Partial<ProductFormData>>({});
  const [categories, setCategories] = useState<Category[]>([]);
  const [questions, setQuestions] = useState<Question[]>([]);
const [isLoading,setIsLoading]=useState(false)

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchQuestions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  // Add fetch functions
  const fetchCategories = async () => {
    const response = await catchAsync(async () => {
      return await getCategories({ limit: 1000 }); // Get all categories without pagination
    });
    if (response && response.data) {
      // Admin API returns paginated response with results array
      const categoriesData = response.data.results || response.data;
      setCategories(Array.isArray(categoriesData) ? categoriesData : []);
    } else {
      setCategories([]);
    }
  };

  const fetchQuestions = async () => {
    const response = await catchAsync(async () => {
      return await getQuestions({ limit: 1000 }); // Get all questions without pagination
    });
    if (response && response.data) {
      // Admin API returns paginated response with results array
      const questionsData = response.data.results || response.data;
      setQuestions(Array.isArray(questionsData) ? questionsData : []);
    } else {
      setQuestions([]);
    }
  };

  const fetchProducts = async () => {
    setLoading(true);
    catchAsync(async () => {
      const response = await getProducts({
        page: params.page,
        limit: params.limit,
        sort:
          params.sortField && params.sortOrder
            ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
            : undefined,
        search: params.search,
      });
      console.log(response.data);
      setProducts(response.data.results);
      setTotalRecords(response.data.totalResults);
    }).finally(() => setLoading(false));
  };

  // const handleCreate = () => {
  //   setSelectedProduct(null);
  //   setFormData({});
  //   setEditMode(false);
  //   setVisibleDialog(true);
  // };

  const handleEdit = (product: Product) => {
    setSelectedProduct(product);
    setFormData(mapProductToFormData(product));
    setEditMode(true);
    setVisibleDialog(true);
  };

  const handleDelete = (productId: string) => {
    confirmDialog({
      message: 'Are you sure you want to delete this product?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        catchAsync(async () => {
          const response = await deleteProduct(productId);
          if (response.status) {
            showSuccess(response.message ?? 'Product deleted successfully');
            fetchProducts();
          } else {
            throw new Error(response.message ?? 'Failed to delete product');
          }
        }, { showToast: true });
      },
    });
  };

  const handleSubmit = async (values: ProductFormData) => {
    catchAsync(async () => {

      if (editMode) {
        setIsLoading(true)
        // For update, attach the product id to the values
        values.product = selectedProduct?._id;
        const formDatas = handelFormData(values)

        // return
   
        const response = await updateProduct(formDatas);
        if (response.status) {
          showSuccess(response.message ?? 'Product updated successfully');
        } else {
          throw new Error(response.message || 'Failed to update product');
        }
      }
      setVisibleDialog(false);
      fetchProducts();
    }, { showToast: true }).finally(()=>setIsLoading(false));
  };

  const categoryBodyTemplate = (rowData: Product) => {
    const categoryId = typeof rowData.category === 'string'
      ? rowData.category
      : rowData.category?._id;

    const cat = categories.find(cat => cat._id === categoryId);
    return cat?.name || '—';
  };


  const subCategoryBodyTemplate = (rowData: Product) => {
    const subCategoryId = typeof rowData.subCategory === 'string'
      ? rowData.subCategory
      : rowData.subCategory?._id;

    const sub = categories
      .flatMap(cat => cat.subCategories || [])
      .find(sub => sub._id === subCategoryId);

    return sub?.name || '—';
  };


const getProviderName = (rowData: Product) => {
  const provider = rowData?.providerDetails?.find(
    (prov) => prov._id === rowData.provider
  );
  return provider?.name || '—';
};



  const actionBodyTemplate = (rowData: Product) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-success w-4 h-4"
          onClick={() => handleEdit(rowData)}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-danger w-4 h-4"
          onClick={() => handleDelete(rowData._id)}
        />
      </div>
    );
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchText);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchText]);

  console.log("formData",selectedProduct)
  return (
    <div className="card p-fluid">
      <ConfirmDialog />

      <div className="flex justify-content-between align-items-center mb-4">
        <h2>Products Management</h2>
        <div className="flex gap-2">
          <span className="p-input-icon-left">
            <IconField iconPosition="left">
              <InputIcon className="pi pi-search" />
              <InputText
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Search products..."
              />
            </IconField>
          </span>
          {/* <Button label="Create Product" icon="pi pi-plus" onClick={handleCreate} /> */}
        </div>
      </div>

      <DataTable
        value={products}
        lazy
        paginator
        first={(params.page - 1) * params.limit}
        rows={params.limit}
        totalRecords={totalRecords}
        onPage={onPage}
        onSort={onSort}
        sortField={params.sortField}
        sortOrder={params.sortOrder}
        loading={loading}
        rowsPerPageOptions={[10, 20, 50]}
        className="p-datatable-striped"
        removableSort
      >
        <Column field="name" header="Name" sortable />
        <Column header="Category" body={categoryBodyTemplate} sortable />
        <Column header="Provider Name" body={getProviderName} sortable />
        <Column header="Subcategory" body={subCategoryBodyTemplate} sortable />
        <Column header="Actions" body={actionBodyTemplate} />
      </DataTable>

      <ProductForm
        isLoading={isLoading}
        visible={visibleDialog}
        onHide={() => setVisibleDialog(false)}
        onSubmit={handleSubmit}
        initialValues={formData}
        editMode={editMode}
        categories={categories}
        questions={questions}
      />
    </div>
  );
};

export default Products;
